import React, { useState, useEffect } from 'react';
import { Modal, Select, Button, Input, Tabs, Radio, message } from 'antd';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import './index.less';
import { addDashboardChart, getTables, getDatasourceById } from '@/services/DataLoom/yibiaopanjiekou';
import { getMysqlTableFields, downloadFile } from '@/services/DataLoom/fileController';
import { CHART_TYPES, CHART_STYLE } from '@/config/charts';
import { useModel } from '@umijs/max';
import * as XLSX from 'xlsx';
import ChartSettings from '@/components/ChartSettings';
import ReactEcharts from 'echarts-for-react';
import { dataPreview } from '@/services/DataLoom/yibiaopanjiekou';

// 组件属性定义
interface ChartSelectorModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: () => void;
  dashboardId: string;
  datasourceId: string;
  datasourceType: string;
}

// 字段类型
const FIELD_TYPE = {
  DIMENSION: 'dimension',
  VALUE: 'value',
};

interface Field {
  name: string; // 显示名称
  originName?: string; // 原始字段名，用于后端接口
  type: string;
}

// 拖拽item类型
interface DragFieldItem extends Field {}

// 拖拽类型
const DRAG_TYPE = {
  FIELD: 'field',
};

// 静态图表类型示例
const staticChartTypes = [
  { id: 'line', name: '折线图', image: '' },
  { id: 'bar', name: '柱状图', image: '' },
  { id: 'pie', name: '饼图', image: '' },
  { id: 'scatter', name: '散点图', image: '' },
  { id: 'funnel', name: '漏斗图', image: '' },
  { id: 'wordcloud', name: '词云', image: '' },
  { id: 'combo', name: '组合图', image: '' },
];

// 字段Item组件（可拖拽）
const FieldItem: React.FC<{
  field: Field;
  isInDimensions?: boolean;
  isInValues?: boolean;
}> = ({ field, isInDimensions = false, isInValues = false }) => {
  const [{ isDragging }, drag] = useDrag<DragFieldItem, void, { isDragging: boolean }>({
    type: DRAG_TYPE.FIELD,
    item: { ...field },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  // 如果字段已经在某个区域，则显示不同的样式
  const isUsed = isInDimensions || isInValues;
  const usedType = isInDimensions ? '维度' : isInValues ? '数值' : '';

  return (
    <div
      ref={drag}
      className={`field-item${isDragging ? ' dragging' : ''}${isUsed ? ' used' : ''}`}
      style={{ opacity: isDragging ? 0.5 : 1 }}
      title={isUsed ? `已用于${usedType}` : ''}
    >
      <div className="field-left">
        <span className="field-icon">Aa</span>
        {field.name}
      </div>
      {isUsed && <span className="used-indicator">已用</span>}
    </div>
  );
};

const { TabPane } = Tabs;

const ChartSelectorModal: React.FC<ChartSelectorModalProps> = ({
  visible,
  onCancel,
  onConfirm,
  dashboardId,
  datasourceId,
  datasourceType,
}) => {
  const { initialState } = useModel('@@initialState');
  const currentUser = initialState?.currentUser;

  // 状态管理
  const [selectedChart, setSelectedChart] = useState<any | null>(null);
  const [dataTableList, setDataTableList] = useState<any[]>([]);
  const [dataTable, setDataTable] = useState<string>('');
  const [tableFields, setTableFields] = useState<string>('');
  const [tableFieldList, setTableFieldList] = useState<any[]>([]);
  const [statType, setstatType] = useState<number>(0);
  const [selectedFields, setSelectedFields] = useState<Array<{ field: string; method: string; label: string }>>([]);
  // 新增：卡片命名
  const [cardName, setCardName] = useState('');
  // 卡片名称编辑状态
  const [isEditingCardName, setIsEditingCardName] = useState(false);
  // 是否手动编辑过卡片名称
  const [isCardNameManuallyEdited, setIsCardNameManuallyEdited] = useState(false);
  // 拖拽到配置区的字段
  const [selectedDimensions, setSelectedDimensions] = useState<Field[]>([]);
  const [selectedValues, setSelectedValues] = useState<Field[]>([]);
  const [selectedChartType, setSelectedChartType] = useState<string>('bar');
  // 新增：中间栏分段控制器切换
  const [centerTab, setCenterTab] = useState<'draw' | 'params'>('draw');
  // Excel 文件相关状态
  const [excelFields, setExcelFields] = useState<Field[]>([]);
  const [datasourceInfo, setDatasourceInfo] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  // 图表设置相关状态
  const [chartSettings, setChartSettings] = useState<any>(CHART_STYLE);
  // 添加图表预览选项状态
  const [previewChartOption, setPreviewChartOption] = useState<any>(null);

  // 维度区拖拽接收
  const [{ isOverDim, canDropDim }, dropDim] = useDrop<DragFieldItem, void, { isOverDim: boolean; canDropDim: boolean }>({
    accept: DRAG_TYPE.FIELD,
    canDrop: (item: DragFieldItem) => {
      // 如果字段已经在数值区，则不能拖入维度区
      if (selectedValues.find((f) => f.name === item.name)) {
        return false;
      }
      // 如果已经有维度字段且不是当前字段，则不能再拖入（限制只能选择一个维度）
      if (selectedDimensions.length > 0 && !selectedDimensions.find((f) => f.name === item.name)) {
        return false;
      }
      return true;
    },
    drop: (item: DragFieldItem) => {
      if (!selectedDimensions.find((f) => f.name === item.name)) {
        // 限制只能选择一个维度，替换现有维度
        setSelectedDimensions([item]);
      }
    },
    collect: (monitor) => ({
      isOverDim: monitor.isOver(),
      canDropDim: monitor.canDrop(),
    }),
  });
  // 数值区拖拽接收
  const [{ isOverVal, canDropVal }, dropVal] = useDrop<DragFieldItem, void, { isOverVal: boolean; canDropVal: boolean }>({
    accept: DRAG_TYPE.FIELD,
    canDrop: (item: DragFieldItem) => {
      // 如果是统计记录总数模式，不允许拖拽到数值区
      if (statType === 0) return false;
      // 如果字段已经在维度区，则不能拖入数值区
      return !selectedDimensions.find((f) => f.name === item.name);
    },
    drop: (item: DragFieldItem) => {
      if (!selectedValues.find((f) => f.name === item.name)) {
        setSelectedValues([...selectedValues, item]);
        // 同时更新 selectedFields，为统计字段数值功能做准备
        if (statType === 1) {
          const fieldKey = item.originName || item.name; // 使用originName作为key
          const existingField = selectedFields.find((f) => f.field === fieldKey);
          if (!existingField) {
            setSelectedFields((prev) => [
              ...prev,
              {
                field: fieldKey, // 使用originName
                method: 'SUM',
                label: item.name, // 显示名称用于界面显示
              },
            ]);
          }
        }
      }
    },
    collect: (monitor) => ({
      isOverVal: monitor.isOver(),
      canDropVal: monitor.canDrop(),
    }),
  });
  // 移除字段
  const removeDimension = (name: string) => {
    setSelectedDimensions(selectedDimensions.filter((f) => f.name !== name));
  };
  const removeValue = (name: string) => {
    const fieldToRemove = selectedValues.find((f) => f.name === name);
    setSelectedValues(selectedValues.filter((f) => f.name !== name));
    // 同时从 selectedFields 中移除，使用originName作为key
    if (fieldToRemove) {
      const fieldKey = fieldToRemove.originName || fieldToRemove.name;
      setSelectedFields(selectedFields.filter((f) => f.field !== fieldKey));
    }
  };

  // 纵轴类型选项
  const statTypeOptions = [
    { value: 0, label: '统计记录总数' },
    { value: 1, label: '统计字段数值' },
  ];

  const statMethodOptions = [
    { value: 'MAX', label: '最大值' },
    { value: 'MIN', label: '最小值' },
    { value: 'SUM', label: '求和' },
    { value: 'AVERAGE', label: '平均值' },
    { value: 'COUNT', label: '计数' },
  ];

  // 动态生成卡片名称
  const generateCardName = () => {
    if (isCardNameManuallyEdited) {
      return; // 如果用户手动编辑过，不再自动生成
    }

    const chartTypeConfig = CHART_TYPES.find((chart) => chart.id === selectedChartType);
    const chartTypeName = chartTypeConfig?.name || '图表';

    // 生成AI分析图表风格的名称
    const generatedName = `AI分析${chartTypeName}_${new Date().toLocaleString()}`;

    setCardName(generatedName);
  };

  // 选择图表类型
  const handleChartSelect = (chart: any) => {
    setSelectedChart(chart);
  };

  // 处理卡片名称编辑
  const handleCardNameEdit = () => {
    setIsEditingCardName(true);
  };

  // 处理卡片名称变化
  const handleCardNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCardName(e.target.value);
    setIsCardNameManuallyEdited(true); // 标记为手动编辑
  };

  // 处理卡片名称保存
  const handleCardNameSave = () => {
    if (cardName.trim() === '') {
      // 如果为空，重新生成默认名称
      generateCardName();
      setIsCardNameManuallyEdited(false);
    }
    setIsEditingCardName(false);
  };

  // 处理卡片名称取消编辑
  const handleCardNameCancel = () => {
    // 取消编辑时恢复到之前的状态
    generateCardName();
    setIsCardNameManuallyEdited(false);
    setIsEditingCardName(false);
  };

  // 处理卡片名称输入框回车
  const handleCardNameKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleCardNameSave();
    } else if (e.key === 'Escape') {
      handleCardNameCancel();
    }
  };

  // 处理表变化
  const handleTableChange = async (newTable: string) => {
    setDataTable(newTable);
  };

  // 处理横轴变化
  const handleFieldChange = async (newField: string) => {
    setTableFields(newField);
  };

  // 处理纵轴类型变化
  const handleOptionChange = async (newOption: number) => {
    setstatType(newOption);
    // 当切换到统计字段数值时，为已选择的数值字段初始化统计方法
    if (newOption === 1 && selectedValues.length > 0) {
      const newFields = selectedValues.map((field) => {
        const fieldKey = field.originName || field.name;
        const existingField = selectedFields.find((f) => f.field === fieldKey);
        return {
          field: fieldKey, // 使用originName
          method: existingField?.method || 'SUM',
          label: field.name, // 显示名称
        };
      });
      setSelectedFields(newFields);
    } else if (newOption === 0) {
      // 切换到统计记录总数时，清空字段选择和数值字段
      setSelectedFields([]);
      setSelectedValues([]);
    }
  };

  // 处理纵轴字段选择
  const handleStatFieldChange = (values: string[]) => {
    const newFields = values.map((field) => {
      const existingField = selectedFields.find((f) => f.field === field);
      const fieldInfo = tableFieldList.find((item) => item.value === field);
      return {
        field,
        method: existingField?.method || 'SUM',
        label: fieldInfo?.label || field,
      };
    });
    setSelectedFields(newFields);
  };

  // 处理统计方法变化
  const handleStatMethodChange = (displayName: string, method: string) => {
    // 根据显示名称找到对应的字段，然后使用originName更新selectedFields
    const valueField = selectedValues.find((f) => f.name === displayName);
    if (valueField) {
      const fieldKey = valueField.originName || valueField.name;
      setSelectedFields((prev) => prev.map((item) => (item.field === fieldKey ? { ...item, method, label: item.label } : item)));
    }
  };

  // 处理图表设置变化
  const handleChartSettingsChange = (newSettings: any) => {
    setChartSettings(newSettings);
  };

  // 获取预览图表配置
  const getPreviewChartOption = () => {
    // 如果有预览数据，优先使用预览数据
    if (previewChartOption) {
      console.log('使用预览配置:', selectedChartType, previewChartOption);
      return previewChartOption;
    }

    if (!selectedChartType) return null;

    const chartType = CHART_TYPES.find((chart) => chart.id === selectedChartType);
    if (!chartType) return null;

    // 基于选中的维度和数值字段生成预览数据
    let previewOption = { ...chartType.defaultOption };

    return previewOption;
  };

  // 获取数据源信息并处理 Excel 文件
  const fetchDatasourceInfo = async () => {
    if (!datasourceId) return;

    try {
      setLoading(true);
      const res = await getDatasourceById({ datasourceId: parseInt(datasourceId) });
      if (res.code === 0 && res.data) {
        setDatasourceInfo(res.data);

        // 如果是 Excel 类型，解析文件获取表头
        if (datasourceType === 'excel' && res.data.configuration) {
          await parseExcelFile(res.data.configuration);
        }
      }
    } catch (error) {
      message.error('获取数据源信息失败');
      console.error('获取数据源信息失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 预览数据
  const previewChartData = async () => {
    try {
      setLoading(true);

      // 数据验证
      if (datasourceType !== 'excel') {
        if (!datasourceId || !dataTable) {
          message.error('请先选择数据源和数据表');
          return;
        }
      } else {
        if (!datasourceInfo?.configuration) {
          message.error('请先选择Excel文件');
          return;
        }
      }

      // 构建请求参数
      const requestBody: any = {
        dataType: datasourceType === 'excel' ? 'file' : 'dataSource',
      };

      // 根据数据源类型，只添加对应的字段
      if (datasourceType === 'excel') {
        requestBody.fileDataOption = {
          fileName: datasourceInfo?.configuration,
          seriesArrayType: statType,
          group: selectedDimensions.length > 0 ? selectedDimensions[0].originName || selectedDimensions[0].name : '', // 使用originName
          seriesArray:
            statType === 1
              ? selectedValues.map((field) => ({
                  fieldName: field.originName || field.name, // 使用originName
                  rollup: selectedFields.find((f) => f.field === (field.originName || field.name))?.method || 'SUM',
                }))
              : [],
        };
      } else {
        requestBody.dataSourceOption = JSON.stringify({
          dataOption: {
            datasourceId: datasourceId,
            dataTableName: dataTable,
            seriesArrayType: statType,
            seriesArray:
              statType === 1
                ? selectedValues.map((field) => ({
                    fieldName: field.originName || field.name, // 使用originName
                    rollup: selectedFields.find((f) => f.field === (field.originName || field.name))?.method || 'SUM',
                  }))
                : [],
            group: selectedDimensions.length > 0 ? selectedDimensions[0].originName || selectedDimensions[0].name : '', // 使用originName
          },
        });
      }

      // 调用数据预览接口
      const response = await dataPreview(requestBody);

      // 存在seriesDataList和 xarrayData 两个字段
      if (response && response.seriesDataList && response.xarrayData) {
        updateChartWithPreviewData(response);
      } else {
        message.error('数据预览失败');
      }
    } catch (error) {
      message.error('数据预览请求失败');
      console.error('数据预览失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 使用预览数据更新图表
  const updateChartWithPreviewData = (previewData: any) => {
    if (!previewData || !selectedChartType) return;

    try {
      // 获取基础图表配置
      const chartType = CHART_TYPES.find((chart) => chart.id === selectedChartType);
      if (!chartType) return;

      let previewOption = { ...chartType.defaultOption };

      // 整理后端返回的数据结构
      const xAxisData = previewData.xarrayData?.values || [];
      const seriesDataList = previewData.seriesDataList || [];

      // 根据不同图表类型处理数据
      switch (selectedChartType) {
        case 'bar':
        case 'line':
          previewOption = {
            xAxis: {
              type: 'category',
              data: xAxisData,
            },
            yAxis: {
              type: 'value',
            },
            series: seriesDataList.map((item: any) => ({
              name: item.title,
              type: selectedChartType,
              data: item.data,
            })),
          };
          break;

        case 'pie':
          const pieData =
            seriesDataList.length > 0 && seriesDataList[0].data
              ? xAxisData.map((name: string, index: number) => ({
                  name: name,
                  value: seriesDataList[0].data[index],
                }))
              : [];

          previewOption = {
            series: [
              {
                type: 'pie',
                data: pieData,
              },
            ],
          };
          break;

        case 'horizontal-bar':
          previewOption = {
            yAxis: {
              type: 'category',
              data: xAxisData,
            },
            xAxis: {
              type: 'value',
            },
            series: seriesDataList.map((item: any) => ({
              name: item.title,
              type: 'bar',
              data: item.data,
            })),
          };
          break;

        case 'scatter':
          previewOption = {
            xAxis: {
              type: 'value',
            },
            yAxis: {
              type: 'value',
            },
            series: seriesDataList.map((item: any) => ({
              name: item.title,
              type: 'scatter',
              data: item.data.map((value: any, idx: number) => [idx, value]),
            })),
          };
          break;

        case 'funnel':
          const funnelData =
            seriesDataList.length > 0 && seriesDataList[0].data
              ? seriesDataList[0].data.map((value: any, index: number) => ({
                  name: xAxisData[index] || `项目${index + 1}`,
                  value: value,
                }))
              : [];

          previewOption = {
            series: [
              {
                type: 'funnel',
                data: funnelData,
              },
            ],
          };
          break;

        case 'combo':
          if (seriesDataList.length >= 2) {
            previewOption = {
              xAxis: {
                type: 'category',
                data: xAxisData,
              },
              yAxis: [
                {
                  type: 'value',
                },
                {
                  type: 'value',
                },
              ],
              series: [
                {
                  name: seriesDataList[0].title,
                  type: 'bar',
                  data: seriesDataList[0].data,
                },
                {
                  name: seriesDataList[1].title,
                  type: 'line',
                  yAxisIndex: 1,
                  data: seriesDataList[1].data,
                },
              ],
            };
          }
          break;

        case 'word-cloud':
          const wordCloudData: Array<{ name: string; value: any }> = [];
          if (xAxisData.length > 0 && seriesDataList.length > 0) {
            for (let i = 0; i < xAxisData.length; i++) {
              let totalValue = 0;
              seriesDataList.forEach((series: any) => {
                if (series.data && series.data[i] !== undefined) {
                  totalValue += Number(series.data[i]) || 0;
                }
              });

              if (totalValue > 0) {
                wordCloudData.push({
                  name: xAxisData[i],
                  value: totalValue,
                });
              }
            }
          }

          if (wordCloudData.length > 0) {
            previewOption = {
              series: [
                {
                  type: 'wordCloud',
                  data: wordCloudData,
                },
              ],
            };
          }
          break;

        default:
          // 其他图表类型保持默认配置
          break;
      }

      // 更新图表设置
      console.log('设置预览配置:', selectedChartType, previewOption);
      setPreviewChartOption(previewOption);
    } catch (error) {
      console.error('更新图表预览失败:', error);
    }
  };

  // 解析 Excel 文件获取表头
  const parseExcelFile = async (fileName: string) => {
    if (!currentUser?.id) {
      message.error('用户信息获取失败');
      return;
    }

    try {
      const response = await downloadFile({
        fileName: fileName,
        userId: currentUser.id.toString(),
      });

      const buffer = await response.arrayBuffer();
      const fileExtension = fileName.split('.').pop()?.toLowerCase();

      if (fileExtension === 'xls' || fileExtension === 'xlsx') {
        const workbook = XLSX.read(buffer, { type: 'array' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        const raw = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as string[][];

        if (raw && raw.length > 0) {
          // 获取第一行作为表头
          const headers = raw[0] || [];
          const fields: Field[] = headers.map((header, index) => {
            const fieldName = header?.toString() || `字段${index + 1}`;
            return {
              name: fieldName, // 显示名称
              originName: fieldName, // Excel中原始名称和显示名称相同
              type: FIELD_TYPE.DIMENSION, // 统一设为维度类型，不再区分
            };
          });
          setExcelFields(fields);
        }
      } else {
        message.error('不支持的文件格式');
      }
    } catch (error) {
      message.error('解析 Excel 文件失败');
      console.error('解析 Excel 文件失败:', error);
    }
  };

  // 确认选择
  const handleConfirm = () => {
    // 验证必填项
    if (!cardName.trim()) {
      message.error('请输入卡片名称');
      return;
    }

    if (!selectedChartType) {
      message.error('请选择图表类型');
      return;
    }

    if (selectedDimensions.length === 0) {
      message.error('请选择一个维度字段');
      return;
    }

    if (statType === 1 && selectedValues.length === 0) {
      message.error('统计字段数值时请选择至少一个数值字段');
      return;
    }

    const chartTypeConfig = CHART_TYPES.find((chart) => chart.id === selectedChartType);
    const baseParams = {
      dashboardId: dashboardId,
      chartName: cardName.trim(), // 使用用户输入的卡片名称
      chartOption: JSON.stringify(chartTypeConfig?.defaultOption || {}),
      chartType: selectedChartType,
      chartStyle: JSON.stringify(chartSettings), // 使用实际的图表设置
    };

    // 根据数据源类型构造不同的 dataOption
    let dataOptionContent: any;

    if (datasourceType === 'excel') {
      // Excel 文件数据源
      dataOptionContent = {
        dataType: 'file',
        fileDataOption: {
          fileName: datasourceInfo?.configuration,
          seriesArrayType: statType,
          group: selectedDimensions.length > 0 ? selectedDimensions[0].originName || selectedDimensions[0].name : '',
          seriesArray:
            statType === 1
              ? selectedFields.map((field) => ({
                  fieldName: field.field, // selectedFields中的field已经是originName
                  rollup: field.method,
                }))
              : [],
        },
      };
    } else {
      // 数据库数据源
      dataOptionContent = {
        dataType: 'dataSource',
        dataOption: {
          datasourceId: datasourceId,
          dataTableName: dataTable,
          seriesArrayType: statType,
          seriesArray:
            statType === 1
              ? selectedFields.map((field) => ({
                  fieldName: field.field,
                  rollup: field.method,
                }))
              : [],
          group: tableFields,
        },
      };
    }

    const params = {
      ...baseParams,
      dataOption: JSON.stringify(dataOptionContent),
    };

    addDashboardChart(params)
      .then((res) => {
        const { code } = res;
        if (code === 0) {
          message.success('添加图表成功');
          onConfirm();
        } else {
          message.error('添加图表失败');
        }
      })
      .catch((err) => {
        message.error(`添加图表失败: ${err.message}`);
      });
  };

  // 图表类型切换时清理数据和配置
  useEffect(() => {
    // 清理预览图表配置，强制重新生成
    setPreviewChartOption(null);
    // 重置图表设置为默认值，避免不同图表类型间的配置冲突
    setChartSettings({ ...CHART_STYLE });
    // 注意：不清理 selectedDimensions 和 selectedValues，让用户保持字段选择
    // 预览数据会在下一次调用预览接口时重新获取
  }, [selectedChartType]);

  // 动态生成卡片名称
  useEffect(() => {
    generateCardName();
  }, [selectedChartType, selectedDimensions, selectedValues, statType]);

  // 当统计方式、维度、数值或图表类型发生变化时自动调用预览接口
  useEffect(() => {
    // 检查是否有足够的配置来进行预览
    const hasValidConfig = () => {
      // 必须选择了图表类型
      if (!selectedChartType) return false;

      // 必须有维度字段
      if (selectedDimensions.length === 0) return false;

      // 如果是统计字段数值模式，必须有数值字段
      if (statType === 1 && selectedValues.length === 0) return false;

      // 对于数据库数据源，必须选择了数据表
      if (datasourceType !== 'excel' && !dataTable) return false;

      // 对于Excel数据源，必须有数据源信息
      if (datasourceType === 'excel' && !datasourceInfo?.configuration) return false;

      return true;
    };

    // 只有在配置有效时才调用预览
    if (hasValidConfig()) {
      previewChartData();
    }
  }, [selectedChartType, selectedDimensions, selectedValues, statType, dataTable, datasourceInfo]);

  // 组件加载时获取数据源信息
  useEffect(() => {
    if (visible && datasourceId) {
      fetchDatasourceInfo();
    }
  }, [visible, datasourceId, datasourceType]);

  // 查询数据源表
  useEffect(() => {
    if (dashboardId && visible) {
      const params = {
        datasourceId: datasourceId,
      };
      getTables(params).then((res) => {
        const { code, data } = res;
        if (code === 0) {
          const newData = data.map((item: any) => {
            return {
              label: item.tableName,
              value: item.tableName,
            };
          });
          setDataTableList(newData);
          // 默认赋值
          if (newData && newData.length > 0) {
            setDataTable(newData[0].value);
          } else {
            setDataTable('');
          }
        }
      });
    }
  }, [visible, dashboardId]);
  // 查询横轴
  useEffect(() => {
    if (dashboardId && dataTable && visible) {
      const params = {
        datasourceId: datasourceId,
        tableName: dataTable,
      };
      getMysqlTableFields(params)
        .then((res) => {
          const { code, data } = res;
          if (code === 0) {
            const newData = data.map((item: any) => {
              return {
                label: item.name || item.originName,
                value: item.originName,
              };
            });
            setTableFieldList(newData);
            // 默认赋值
            if (newData && newData.length > 0) {
              setTableFields(newData[0].value);
            } else {
              setTableFields('');
            }
          }
        })
        .catch((err) => {
          message.error(`获取表 ${dataTable} 结构失败: ${err.message}`);
        });
    }
  }, [visible, dashboardId, dataTable]);

  return (
    <Modal
      title="新增图表"
      open={visible}
      onCancel={onCancel}
      footer={null}
      width="90%"
      className="chart-selector-modal common-modal"
      maskClosable={false}
      getContainer={false}
    >
      <DndProvider backend={HTML5Backend}>
        <div className="add-chart-modal-layout">
          {/* 左侧栏 */}
          <div className="add-chart-sidebar">
            {/* 只有非 Excel 数据源才显示数据表选择 */}
            {datasourceType !== 'excel' && (
              <div className="sidebar-section">
                <div className="sidebar-title">数据表</div>
                <Select
                  style={{ width: 'calc(100% - 24px)' }}
                  placeholder="请选择数据表"
                  value={dataTable}
                  onChange={handleTableChange}
                  options={dataTableList}
                />
                {/* <div className="sidebar-link">
                  预览数据表
                  <img src="/assets/image_1752055460885_5lit3y.svg" alt="" />
                </div> */}
              </div>
            )}
            <div className="sidebar-section">
              <div className="sidebar-title">字段</div>
              <div className="field-list">
                {loading ? (
                  <div style={{ textAlign: 'center', padding: '20px' }}>加载中...</div>
                ) : datasourceType === 'excel' ? (
                  // Excel 数据源显示解析出的所有字段
                  excelFields.map((field, idx) => (
                    <FieldItem
                      key={field.name + idx}
                      field={field}
                      isInDimensions={selectedDimensions.some((f) => f.name === field.name)}
                      isInValues={selectedValues.some((f) => f.name === field.name)}
                    />
                  ))
                ) : (
                  // 其他数据源显示从数据库获取的真实字段
                  <>
                    {tableFieldList.map((field, idx) => {
                      const fieldObj = {
                        name: field.label, // 显示名称
                        originName: field.value, // 原始字段名
                        type: FIELD_TYPE.DIMENSION, // 可以根据字段类型进行判断
                      };
                      return (
                        <FieldItem
                          key={field.value + idx}
                          field={fieldObj}
                          isInDimensions={selectedDimensions.some((f) => f.name === fieldObj.name)}
                          isInValues={selectedValues.some((f) => f.name === fieldObj.name)}
                        />
                      );
                    })}
                  </>
                )}
              </div>
            </div>
          </div>
          {/* 中间栏 */}
          <div className="add-chart-center">
            {/* Radio.Group 单选框组 替换 Segmented */}
            <Radio.Group
              options={[
                { label: '绘制', value: 'draw' },
                { label: '参数设置', value: 'params' },
              ]}
              value={centerTab}
              onChange={(e) => setCenterTab(e.target.value)}
              optionType="button"
              buttonStyle="solid"
              style={{ marginBottom: 16, width: '100%' }}
            />
            {centerTab === 'draw' && (
              <div className="config-section">
                <div className="config-title">
                  <span style={{ color: 'red', marginRight: 4 }}>*</span>统计方式
                </div>
                <Select
                  style={{ width: '100%' }}
                  placeholder="请选择统计方式"
                  value={statType}
                  onChange={handleOptionChange}
                  options={statTypeOptions}
                />
                <div className="config-title">
                  <span style={{ color: 'red', marginRight: 4 }}>*</span>维度
                  <span style={{ color: '#999', fontSize: '12px', marginLeft: '8px' }}>(只能选择一个维度字段)</span>
                </div>
                <div ref={dropDim} className={`drop-area${isOverDim ? (canDropDim ? ' drop-hover' : ' drop-disabled') : ''}`}>
                  {selectedDimensions.length === 0 && <span style={{ color: '#bbb' }}>拖到这里（只能选择一个维度）</span>}
                  {selectedDimensions.map((field) => (
                    <div className="selected-field" key={field.name}>
                      <div className="field-content">
                        <div className="field-left">
                          <span className="field-icon">Aa</span>
                          <span className="field-name">{field.name}</span>
                        </div>
                      </div>
                      <span className="remove-btn" onClick={() => removeDimension(field.name)}>
                        ×
                      </span>
                    </div>
                  ))}
                </div>
                <div className="config-title">
                  <span style={{ color: 'red', marginRight: 4 }}>*</span>数值
                  {statType === 0 && (
                    <span style={{ color: '#999', fontSize: '12px', marginLeft: '8px' }}>(统计记录总数时无需选择字段)</span>
                  )}
                </div>
                <div
                  ref={dropVal}
                  className={`drop-area${isOverVal ? (canDropVal ? ' drop-hover' : ' drop-disabled') : ''}${statType === 0 ? ' disabled' : ''}`}
                  style={{ opacity: statType === 0 ? 0.5 : 1, pointerEvents: statType === 0 ? 'none' : 'auto' }}
                >
                  {selectedValues.length === 0 && (
                    <span style={{ color: '#bbb' }}>{statType === 0 ? '统计记录总数时无需选择字段' : '拖到这里'}</span>
                  )}
                  {selectedValues.map((field) => (
                    <div className="selected-field" key={field.name}>
                      <div className="field-content">
                        <div className="field-left">
                          <span className="field-icon">Σ</span>
                          <span className="field-name">{field.name}</span>
                        </div>
                        {statType === 1 && (
                          <Select
                            size="small"
                            value={selectedFields.find((f) => f.field === (field.originName || field.name))?.method || 'SUM'}
                            onChange={(method) => handleStatMethodChange(field.name, method)}
                            options={statMethodOptions}
                            className="stat-method-select"
                            style={{ minWidth: 75 }}
                          />
                        )}
                      </div>
                      <span className="remove-btn" onClick={() => removeValue(field.name)}>
                        ×
                      </span>
                    </div>
                  ))}
                </div>
                <div className="sidebar-section">
                  <div className="sidebar-title">图表</div>
                  <div className="chart-type-list">
                    {CHART_TYPES.map((chart) => (
                      <div
                        key={chart.id}
                        className={`chart-type-item${selectedChartType === chart.id ? ' selected' : ''}`}
                        onClick={() => setSelectedChartType(chart.id)}
                      >
                        <div className="chart-image">
                          {/* 可替换为chart.image */}
                          <img src={chart.image} alt={chart.name} />
                        </div>
                        <div className="chart-name">{chart.name}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
            {centerTab === 'params' && (
              <div className="config-section">
                <ChartSettings
                  onChange={handleChartSettingsChange}
                  defaultValues={chartSettings}
                  chartType={selectedChartType as any}
                  layout="vertical"
                />
              </div>
            )}
          </div>
          {/* 右侧栏 */}
          <div className="add-chart-preview">
            <div className="preview-header">
              <div className="preview-header-left">
                {isEditingCardName ? (
                  <Input
                    value={cardName}
                    onChange={handleCardNameChange}
                    onBlur={handleCardNameSave}
                    onKeyDown={handleCardNameKeyPress}
                    className="card-name-input editing"
                    autoFocus
                    placeholder="请输入卡片名称"
                  />
                ) : (
                  <div className="card-name-display" onClick={handleCardNameEdit}>
                    <span className="card-name-text">{cardName || '新建图表'}</span>
                    <img
                      src="/assets/image_1752116986733_ra25s0.svg"
                      alt="修改"
                      style={{ cursor: 'pointer', marginLeft: '8px' }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCardNameEdit();
                      }}
                    />
                  </div>
                )}
              </div>
              <div className="preview-header-right">
                <Button
                  style={{ marginRight: 8 }}
                  onClick={() => {
                    setSelectedDimensions([]);
                    setSelectedValues([]);
                    setSelectedFields([]);
                    setSelectedChartType('bar');
                  }}
                >
                  重置
                </Button>
                <Button type="primary" onClick={handleConfirm}>
                  确认
                </Button>
              </div>
            </div>
            <div className="preview-chart-area">
              {/* 图表预览区 */}
              <div className="chart-preview-container">
                {selectedChartType ? (
                  <ReactEcharts
                    key={selectedChartType}
                    option={getPreviewChartOption()}
                    style={{ height: '100%', width: '100%' }}
                    opts={{ renderer: 'canvas' }}
                  />
                ) : (
                  <div className="no-chart-selected">
                    <div className="placeholder-text">请选择图表类型</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </DndProvider>
    </Modal>
  );
};

export default ChartSelectorModal;
