// 图表类型定义
type ChartType = {
  id: string;
  name: string;
  image: string;
  defaultOption: {
    title?: any;
    tooltip?: any;
    legend?: any;
    grid?: any;
    xAxis?: any;
    yAxis?: any;
    series?: any[];
    color?: string[];
    backgroundColor?: string;
    textStyle?: any;
    [key: string]: any;
  };
};

const CHART_STYLE = {
  theme: 'theme1',
  widthRatio: 50,
  showLegend: true,
  showDataLabel: true,
  dataLabelTextSize: '12',
  showDataTips: true,
  showPrefixUnit: false,
  prefixUnitText: '',
  showSuffixUnit: false,
  suffixUnitText: '',
  showAxis: true,
  maxValue: '自动',
  minValue: '自动',
  dataInterval: '自动',
  axisAngle: '0°',
  textSize: '12',
  textColor: '#2868e7',
  showGrid: true,
  gridStyle: '实线',
  gridWidth: '1',
  gridColor: '#2868e7',
  showAnimation: true,
};

// 图表类型数据
const CHART_TYPES: ChartType[] = [
  {
    id: 'bar',
    name: '柱状图',
    image: '/assets/mnssb39i.svg',
    defaultOption: {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      legend: {
        data: ['销售额', '利润'],
      },
      grid: {
        top: 25,
        bottom: 16,
        left: '4%',
        right: '2%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月'],
        axisLabel: {
          interval: 0,
          rotate: 0,
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}万',
        },
      },
      series: [
        {
          name: '销售额',
          type: 'bar',
          data: [285, 342, 398, 456, 523, 612],
          barWidth: '30%',
          itemStyle: {
            borderRadius: [4, 4, 0, 0],
          },
        },
        {
          name: '利润',
          type: 'bar',
          data: [68, 82, 95, 109, 125, 147],
          barWidth: '30%',
          itemStyle: {
            borderRadius: [4, 4, 0, 0],
          },
        },
      ],
      color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
      backgroundColor: '#ffffff',
    },
  },
  {
    id: 'line',
    name: '折线图',
    image: '/assets/9htgl43h.svg',
    defaultOption: {
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: ['网站访问量', '用户注册量'],
      },
      grid: {
        top: 25,
        bottom: 16,
        left: '4%',
        right: '2%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}',
        },
      },
      series: [
        {
          name: '网站访问量',
          type: 'line',
          data: [1820, 2350, 2180, 2650, 3200, 2890, 2450],
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 2,
          },
          areaStyle: {
            opacity: 0.1,
          },
        },
        {
          name: '用户注册量',
          type: 'line',
          data: [156, 189, 175, 212, 268, 235, 198],
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 2,
          },
          areaStyle: {
            opacity: 0.1,
          },
        },
      ],
      color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
      backgroundColor: '#ffffff',
    },
  },
  {
    id: 'pie',
    name: '饼图',
    image: '/assets/acprqf2i.svg',
    defaultOption: {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'middle',
      },
      grid: {
        top: 25,
        bottom: 16,
        left: '4%',
        right: '2%',
        containLabel: true,
      },
      series: [
        {
          name: '数据',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: true,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}: {d}%',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 14,
              fontWeight: 'bold',
            },
          },
          data: [
            { value: 3580, name: '移动端' },
            { value: 2650, name: 'PC端' },
            { value: 1890, name: '平板端' },
            { value: 1250, name: '小程序' },
            { value: 680, name: '其他' },
          ],
        },
      ],
      color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
      backgroundColor: '#ffffff',
    },
  },
  {
    id: 'horizontal-bar',
    name: '条形图',
    image: '/assets/jybw14mr.svg',
    defaultOption: {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      legend: {
        data: ['完成度', '目标值'],
      },
      grid: {
        top: 25,
        bottom: 16,
        left: '4%',
        right: '2%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}%',
        },
      },
      yAxis: {
        type: 'category',
        data: ['产品开发', '市场推广', '客户服务', '技术支持', '运营管理'],
        axisLabel: {
          interval: 0,
        },
      },
      series: [
        {
          name: '完成度',
          type: 'bar',
          data: [85, 92, 78, 88, 95],
          barWidth: '30%',
          itemStyle: {
            borderRadius: [0, 4, 4, 0],
          },
        },
        {
          name: '目标值',
          type: 'bar',
          data: [90, 85, 80, 90, 88],
          barWidth: '30%',
          itemStyle: {
            borderRadius: [0, 4, 4, 0],
          },
        },
      ],
      color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
      backgroundColor: '#ffffff',
    },
  },
  {
    id: 'scatter',
    name: '散点图',
    image: '/assets/udgwbutc.svg',
    defaultOption: {
      tooltip: {
        trigger: 'item',
        formatter: function (params: any) {
          return `X: ${params.data[0]}<br/>Y: ${params.data[1]}<br/>值: ${params.data[2]}`;
        },
      },
      legend: {
        data: ['产品A', '产品B'],
      },
      grid: {
        top: 25,
        bottom: 16,
        left: '4%',
        right: '2%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        scale: true,
        name: '价格(元)',
        axisLabel: {
          formatter: '{value}',
        },
      },
      yAxis: {
        type: 'value',
        scale: true,
        name: '销量(件)',
        axisLabel: {
          formatter: '{value}',
        },
      },
      series: [
        {
          name: '产品A',
          type: 'scatter',
          symbolSize: function (data: any) {
            return Math.sqrt(data[2]) * 3;
          },
          data: [
            [128, 450, 25],
            [156, 380, 32],
            [189, 320, 28],
            [225, 280, 35],
            [268, 220, 42],
            [298, 180, 38],
          ],
        },
        {
          name: '产品B',
          type: 'scatter',
          symbolSize: function (data: any) {
            return Math.sqrt(data[2]) * 3;
          },
          data: [
            [98, 520, 22],
            [125, 460, 29],
            [168, 390, 31],
            [198, 340, 36],
            [235, 290, 40],
            [278, 240, 33],
          ],
        },
      ],
      color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
      backgroundColor: '#ffffff',
    },
  },
  {
    id: 'combo',
    name: '组合图',
    image: '/assets/7si8jsd7.svg',
    defaultOption: {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
        },
      },
      legend: {
        data: ['销售额', '增长率'],
      },
      grid: {
        top: 25,
        bottom: 16,
        left: '4%',
        right: '2%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: ['Q1', 'Q2', 'Q3', 'Q4'],
      },
      yAxis: [
        {
          type: 'value',
          name: '销售额(万元)',
          position: 'left',
          axisLabel: {
            formatter: '{value}',
          },
        },
        {
          type: 'value',
          name: '增长率(%)',
          position: 'right',
          axisLabel: {
            formatter: '{value}%',
          },
        },
      ],
      series: [
        {
          name: '销售额',
          type: 'bar',
          data: [1250, 1680, 1890, 2150],
          barWidth: '30%',
          itemStyle: {
            borderRadius: [4, 4, 0, 0],
          },
        },
        {
          name: '增长率',
          type: 'line',
          yAxisIndex: 1,
          data: [15.2, 34.4, 12.5, 13.8],
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 2,
          },
        },
      ],
      color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
      backgroundColor: '#ffffff',
    },
  },
  {
    id: 'funnel',
    name: '漏斗图',
    image: '/assets/jeejfyi6.svg',
    defaultOption: {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c}%',
      },
      legend: {
        data: ['访问', '咨询', '试用', '购买', '续费'],
      },
      grid: {
        top: 25,
        bottom: 16,
        left: '4%',
        right: '2%',
        containLabel: true,
      },
      series: [
        {
          name: '漏斗图',
          type: 'funnel',
          left: '10%',
          top: 60,
          bottom: 60,
          width: '80%',
          min: 0,
          max: 100,
          minSize: '0%',
          maxSize: '100%',
          sort: 'descending',
          gap: 2,
          label: {
            show: true,
            position: 'inside',
            formatter: '{b}: {c}%',
          },
          labelLine: {
            length: 10,
            lineStyle: {
              width: 1,
              type: 'solid',
            },
          },
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 1,
          },
          emphasis: {
            label: {
              fontSize: 14,
            },
          },
          data: [
            { value: 100, name: '访问' },
            { value: 75, name: '咨询' },
            { value: 45, name: '试用' },
            { value: 28, name: '购买' },
            { value: 18, name: '续费' },
          ],
        },
      ],
      color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
      backgroundColor: '#ffffff',
    },
  },
  {
    id: 'word-cloud',
    name: '词云',
    image: '/assets/2r90fnlb.svg',
    defaultOption: {
      tooltip: { show: true },
      series: [
        {
          type: 'wordCloud',
          shape: 'circle',
          left: 'center',
          top: 'center',
          width: '100%',
          height: '100%',
          sizeRange: [12, 60],
          rotationRange: [-90, 90],
          rotationStep: 45,
          gridSize: 8,
          drawOutOfBound: false,
          textStyle: {
            fontFamily: 'sans-serif',
            fontWeight: 'bold',
            color: () =>
              `rgb(${[Math.round(Math.random() * 160), Math.round(Math.random() * 160), Math.round(Math.random() * 160)].join(',')})`,
          },
          emphasis: {
            focus: 'self',
            textStyle: {
              shadowBlur: 10,
              shadowColor: '#333',
            },
          },
          data: [
            { name: '人工智能', value: 150 },
            { name: '大数据', value: 120 },
            { name: '云计算', value: 100 },
            { name: '物联网', value: 85 },
            { name: '区块链', value: 70 },
            { name: '机器学习', value: 65 },
            { name: '深度学习', value: 60 },
            { name: '数据分析', value: 55 },
            { name: '算法优化', value: 50 },
            { name: '智能推荐', value: 45 },
            { name: '自然语言处理', value: 40 },
            { name: '计算机视觉', value: 35 },
            { name: '语音识别', value: 30 },
            { name: '知识图谱', value: 25 },
            { name: '边缘计算', value: 20 },
          ],
        },
      ],
      backgroundColor: '#fff',
    },
  },
];

export { CHART_TYPES, CHART_STYLE };
